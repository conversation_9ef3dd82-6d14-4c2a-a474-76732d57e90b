/**
 * Time Synchronization Service
 * Handles synchronization between client and server time to ensure consistent timestamps
 * and countdown timers across all users.
 */

export interface TimeSyncData {
  clientTime: number;
  serverTime: number;
  roundTripTime: number;
}

export interface TimeSyncStatus {
  synchronized: boolean;
  offset: number; // Server time - client time
  lastSync: number;
  error?: string;
}

class TimeSyncService {
  private offset: number = 0;
  private lastSyncTime: number = 0;
  private syncInterval: NodeJS.Timeout | null = null;
  private readonly SYNC_INTERVAL = 30000; // Sync every 30 seconds
  private readonly MAX_SYNC_AGE = 60000; // Consider sync stale after 1 minute
  private readonly MAX_RETRY_ATTEMPTS = 3;
  private readonly MIN_SYNC_INTERVAL = 5000; // Minimum 5 seconds between syncs
  private listeners: ((status: TimeSyncStatus) => void)[] = [];
  private isInitialized: boolean = false;
  private retryCount: number = 0;
  private preferSocketSync: boolean = true;
  private isSyncing: boolean = false;
  private lastSyncAttempt: number = 0;

  constructor() {
    // Don't auto-start sync in constructor to avoid race conditions
    // Sync will be started when socket connects or manually initiated

    // Try to restore sync state from sessionStorage if available
    this.restoreSyncState();
  }

  /**
   * Restore sync state from sessionStorage
   */
  private restoreSyncState(): void {
    try {
      const stored = sessionStorage.getItem('timeSyncState');
      if (stored) {
        const state = JSON.parse(stored);
        const age = Date.now() - state.lastSyncTime;

        // Only restore if the sync is recent (less than 30 seconds old)
        if (age < 30000 && state.offset !== undefined) {
          this.offset = state.offset;
          this.lastSyncTime = state.lastSyncTime;
          console.log(`Restored time sync state: offset=${this.offset}ms, age=${age}ms`);
        }
      }
    } catch (error) {
      console.warn('Failed to restore time sync state:', error);
    }
  }

  /**
   * Save sync state to sessionStorage
   */
  private saveSyncState(): void {
    try {
      const state = {
        offset: this.offset,
        lastSyncTime: this.lastSyncTime,
      };
      sessionStorage.setItem('timeSyncState', JSON.stringify(state));
    } catch (error) {
      console.warn('Failed to save time sync state:', error);
    }
  }

  /**
   * Initialize the time sync service
   */
  initialize(): void {
    if (this.isInitialized) {
      return;
    }
    this.isInitialized = true;
    console.log('TimeSyncService initialized');

    // If we don't have recent sync data, try HTTP sync immediately
    if (!this.isSynchronized()) {
      console.log('No recent sync data, attempting immediate HTTP sync...');
      this.syncWithServer().catch(error => {
        console.error('Initial HTTP sync failed:', error);
      });
    }
  }

  /**
   * Get the current server time based on synchronized offset
   */
  getServerTime(): number {
    if (!this.isSynchronized()) {
      // If we have a recent offset (even if considered "stale"), use it
      if (this.lastSyncTime > 0 && this.offset !== 0) {
        const age = Date.now() - this.lastSyncTime;
        if (age < 300000) { // Use stale offset for up to 5 minutes
          return Date.now() + this.offset;
        }
      }

      // Only log warning occasionally to avoid spam
      if (Date.now() - this.lastSyncTime > 10000) {
        console.warn('Time not synchronized, using local time');
      }
      return Date.now();
    }
    return Date.now() + this.offset;
  }

  /**
   * Convert a client timestamp to server time
   */
  clientToServerTime(clientTime: number): number {
    return clientTime + this.offset;
  }

  /**
   * Convert a server timestamp to client time
   */
  serverToClientTime(serverTime: number): number {
    return serverTime - this.offset;
  }

  /**
   * Check if time synchronization is current
   */
  isSynchronized(): boolean {
    const age = Date.now() - this.lastSyncTime;
    return age < this.MAX_SYNC_AGE && this.lastSyncTime > 0;
  }

  /**
   * Get current synchronization status
   */
  getStatus(): TimeSyncStatus {
    return {
      synchronized: this.isSynchronized(),
      offset: this.offset,
      lastSync: this.lastSyncTime,
      // Clear error if we're now synchronized
      error: this.isSynchronized() ? undefined : this.lastError,
    };
  }

  private lastError?: string;

  /**
   * Check if we can perform a sync (rate limiting)
   */
  private canSync(): boolean {
    const now = Date.now();
    const timeSinceLastAttempt = now - this.lastSyncAttempt;

    if (this.isSyncing) {
      console.log('Sync already in progress, skipping');
      return false;
    }

    if (timeSinceLastAttempt < this.MIN_SYNC_INTERVAL) {
      console.log(`Rate limiting: ${timeSinceLastAttempt}ms since last sync, minimum is ${this.MIN_SYNC_INTERVAL}ms`);
      return false;
    }

    return true;
  }

  /**
   * Perform time synchronization with server via HTTP (fallback method)
   */
  async syncWithServer(): Promise<TimeSyncStatus> {
    if (!this.canSync()) {
      return this.getStatus();
    }

    this.isSyncing = true;
    this.lastSyncAttempt = Date.now();

    try {
      console.log('Attempting HTTP time sync...');
      const clientSendTime = performance.now();

      const response = await fetch('/api/time', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const clientReceiveTime = performance.now();
      const absoluteReceiveTime = Date.now();
      const data = await response.json();
      const serverTime = data.serverTime;

      // Calculate round trip time using performance.now() for better precision
      const roundTripTime = clientReceiveTime - clientSendTime;

      // Estimate when the server timestamp was taken
      const networkDelay = roundTripTime / 2;
      const estimatedServerTime = serverTime + networkDelay;

      // Calculate offset: server time - client time
      this.offset = estimatedServerTime - absoluteReceiveTime;
      this.lastSyncTime = Date.now();
      this.retryCount = 0; // Reset retry count on success

      // Clear any previous error
      this.lastError = undefined;

      // Save state to sessionStorage
      this.saveSyncState();

      const status = this.getStatus();
      this.notifyListeners(status);

      console.log(`HTTP time sync completed. Offset: ${this.offset.toFixed(2)}ms, RTT: ${roundTripTime.toFixed(2)}ms`);
      return status;
    } catch (error) {
      this.retryCount++;
      this.lastError = error instanceof Error ? error.message : 'Unknown error';

      const status: TimeSyncStatus = {
        synchronized: false,
        offset: this.offset,
        lastSync: this.lastSyncTime,
        error: this.lastError,
      };

      this.notifyListeners(status);
      console.error(`HTTP time sync failed (attempt ${this.retryCount}):`, error);

      return status;
    } finally {
      this.isSyncing = false;
    }
  }

  /**
   * Perform time synchronization via Socket.IO (preferred method)
   */
  syncViaSocket(socket: any): Promise<TimeSyncStatus> {
    return new Promise((resolve) => {
      if (!this.canSync()) {
        resolve(this.getStatus());
        return;
      }

      if (!socket || !socket.connected) {
        console.warn('Socket not connected, falling back to HTTP sync');
        this.syncWithServer().then(resolve);
        return;
      }

      this.isSyncing = true;
      this.lastSyncAttempt = Date.now();

      console.log('Attempting Socket.IO time sync...');
      const clientSendTime = performance.now();
      const absoluteSendTime = Date.now();

      const handleResponse = (data: TimeSyncData) => {
        const clientReceiveTime = performance.now();
        const absoluteReceiveTime = Date.now();

        // Calculate round trip time using performance.now() for better precision
        const roundTripTime = clientReceiveTime - clientSendTime;

        // Estimate when the server timestamp was taken
        const networkDelay = roundTripTime / 2;
        const estimatedServerTime = data.serverTime + networkDelay;

        // Calculate offset: server time - client time
        this.offset = estimatedServerTime - absoluteReceiveTime;
        this.lastSyncTime = Date.now();
        this.retryCount = 0; // Reset retry count on success

        // Clear any previous error
        this.lastError = undefined;

        // Save state to sessionStorage
        this.saveSyncState();

        const status = this.getStatus();
        this.notifyListeners(status);

        console.log(`Socket time sync completed. Offset: ${this.offset.toFixed(2)}ms, RTT: ${roundTripTime.toFixed(2)}ms`);

        // Clean up listener
        socket.off('time_sync_response', handleResponse);
        this.isSyncing = false;
        resolve(status);
      };

      const handleTimeout = () => {
        socket.off('time_sync_response', handleResponse);
        this.isSyncing = false;
        this.retryCount++;

        console.warn(`Socket sync timeout (attempt ${this.retryCount})`);

        // Instead of showing error, fall back to HTTP sync
        if (this.retryCount < this.MAX_RETRY_ATTEMPTS) {
          console.log('Socket timeout, falling back to HTTP sync...');
          this.syncWithServer().then(resolve).catch(() => {
            this.lastError = 'Socket sync timeout, HTTP fallback failed';
            const status: TimeSyncStatus = {
              synchronized: false,
              offset: this.offset,
              lastSync: this.lastSyncTime,
              error: this.lastError,
            };
            this.notifyListeners(status);
            resolve(status);
          });
        } else {
          this.lastError = 'Socket sync timeout after retries';
          const status: TimeSyncStatus = {
            synchronized: false,
            offset: this.offset,
            lastSync: this.lastSyncTime,
            error: this.lastError,
          };
          this.notifyListeners(status);
          resolve(status);
        }
      };

      // Clean up any existing listeners to prevent conflicts
      socket.off('time_sync_response');

      // Set up response listener
      socket.on('time_sync_response', handleResponse);

      // Send sync request with high-precision timestamp
      socket.emit('time_sync_request', {
        clientTime: absoluteSendTime,
        performanceTime: clientSendTime
      });

      // Timeout after 5 seconds (increased from 3)
      setTimeout(handleTimeout, 5000);
    });
  }

  /**
   * Start periodic time synchronization
   */
  startPeriodicSync(socket?: any): void {
    // Prevent multiple intervals
    if (this.syncInterval) {
      console.log('Periodic sync already running, not starting another');
      return;
    }

    console.log(`Starting periodic time sync with interval: ${this.SYNC_INTERVAL}ms`);

    this.syncInterval = setInterval(() => {
      console.log('Periodic sync triggered');
      if (socket && socket.connected && this.preferSocketSync) {
        this.syncViaSocket(socket).catch(error => {
          console.error('Periodic socket sync failed:', error);
          // If socket sync fails, try HTTP as backup
          console.log('Attempting HTTP sync as backup...');
          this.syncWithServer().catch(httpError => {
            console.error('HTTP backup sync also failed:', httpError);
          });
        });
      } else {
        this.syncWithServer().catch(console.error);
      }
    }, this.SYNC_INTERVAL);

    // Perform initial sync
    console.log('Performing initial sync');
    if (socket && socket.connected && this.preferSocketSync) {
      this.syncViaSocket(socket).catch(error => {
        console.error('Initial socket sync failed:', error);
        this.syncWithServer().catch(console.error);
      });
    } else {
      this.syncWithServer().catch(console.error);
    }
  }

  /**
   * Perform immediate sync (used when socket connects)
   */
  performImmediateSync(socket?: any): Promise<TimeSyncStatus> {
    if (socket && socket.connected && this.preferSocketSync) {
      return this.syncViaSocket(socket);
    } else {
      return this.syncWithServer();
    }
  }

  /**
   * Stop periodic synchronization
   */
  stopSync(): void {
    console.log('Stopping periodic time sync');
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
    this.isSyncing = false;
  }

  /**
   * Reset sync state (useful for debugging)
   */
  resetSyncState(): void {
    console.log('Resetting time sync state');
    this.stopSync();
    this.retryCount = 0;
    this.isSyncing = false;
    this.lastSyncAttempt = 0;
  }

  /**
   * Add a listener for sync status changes
   */
  addListener(listener: (status: TimeSyncStatus) => void): void {
    this.listeners.push(listener);
  }

  /**
   * Remove a listener
   */
  removeListener(listener: (status: TimeSyncStatus) => void): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * Notify all listeners of status changes
   */
  private notifyListeners(status: TimeSyncStatus): void {
    this.listeners.forEach(listener => {
      try {
        listener(status);
      } catch (error) {
        console.error('Error in time sync listener:', error);
      }
    });
  }

  /**
   * Format time remaining with synchronized time
   */
  formatTimeRemaining(endTime: Date | number): string {
    const endTimeMs = typeof endTime === 'number' ? endTime : endTime.getTime();
    const now = this.getServerTime();
    const diff = endTimeMs - now;

    if (diff <= 0) {
      return 'Ended';
    }

    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diff % (1000 * 60)) / 1000);

    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  }

  /**
   * Get minutes remaining with synchronized time
   */
  getMinutesRemaining(endTime: Date | number): number {
    const endTimeMs = typeof endTime === 'number' ? endTime : endTime.getTime();
    const now = this.getServerTime();
    const diff = endTimeMs - now;
    return Math.max(0, Math.floor(diff / (1000 * 60)));
  }
}

// Export singleton instance
export const timeSyncService = new TimeSyncService();
export default timeSyncService;
